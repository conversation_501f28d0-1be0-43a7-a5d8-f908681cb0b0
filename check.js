'use strict';
const express = require('express');
const fs = require('fs');
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = 3000;

// Dummy JSON data
const jsonData = {
  name: '<PERSON>',
  email: '<EMAIL>',
  status: 'active',
};

// Path to your file (change to your actual file)
const fileType = 'csv'; // Change to 'pdf' if you want
const fileName = `sample.${fileType}`;
const filePath = path.join(__dirname, fileName);

// Create a dummy CSV file if it doesn't exist
if (!fs.existsSync(filePath)) {
  fs.writeFileSync(filePath, 'id,name\n1,<PERSON>\n2,<PERSON>');
}

app.use(cors());

app.get('/download', async (req, res) => {
  try {
    const csvBuffer = await fs.promises.readFile(filePath);
    const csvBase64 = csvBuffer.toString('base64');

    const pdfFileName = 'TWN_DevOps_Roadmap_for_Software_Engineers.pdf';
    const pdfFilePath = path.join(__dirname, pdfFileName);
    let pdfBuffer;
    if (fs.existsSync(pdfFilePath)) {
      pdfBuffer = await fs.promises.readFile(pdfFilePath);
    }
    const pdfBase64 = pdfBuffer.toString('base64');

    res.json({
      json_data: jsonData,
      csv_file: {
        filename: fileName,
        content_type: 'text/csv',
        content_base64: csvBase64,
      },
      pdf_file: {
        filename: pdfFileName,
        content_type: 'application/pdf',
        content_base64: pdfBase64,
      },
    });
  } catch (err) {
    res.status(500).json({ error: 'Failed to process files', details: err.message });
  }
});

app.listen(PORT, () => {
  console.log(`Server running at http://localhost:${PORT}`);
});
