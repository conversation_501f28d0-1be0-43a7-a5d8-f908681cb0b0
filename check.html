<!DOCTYPE html>
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>CSV and PDF Viewer</title>
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script crossorigin src="https://unpkg.com/babel-standalone@6/babel.min.js"></script>
    <style>
      table {
        border-collapse: collapse;
      }
      th,
      td {
        border: 1px solid #333;
        padding: 4px 8px;
      }
    </style>
  </head>
  <body>
    <div id="root"></div>
    <script type="text/babel">
      function base64ToBlob(base64, contentType) {
        return new Blob([Uint8Array.from(atob(base64), c => c.charCodeAt(0))], {
          type: contentType,
        });
      }

      function parseCSV(csvString) {
        const lines = csvString.trim().split('\n');
        const headers = lines[0].split(',');
        const rows = lines.slice(1).map(line => line.split(','));
        return { headers, rows };
      }

      function CsvAndPdfViewer() {
        const [csvData, setCsvData] = React.useState(null);
        const [loading, setLoading] = React.useState(true);

        React.useEffect(() => {
          async function fetchData() {
            setLoading(true);
            const response = await fetch('http://localhost:3000/download', { mode: 'cors' });
            const data = await response.json();

            // Decode and parse CSV
            const csvBlob = base64ToBlob(data.csv_file.content_base64, data.csv_file.content_type);
            const csvText = await csvBlob.text();
            setCsvData(parseCSV(csvText));

            // Download PDF automatically
            const pdfBlob = base64ToBlob(data.pdf_file.content_base64, data.pdf_file.content_type);
            const pdfUrl = URL.createObjectURL(pdfBlob);
            window.open(pdfUrl, '_blank');
            setTimeout(() => URL.revokeObjectURL(pdfUrl), 10000);
            setLoading(false);
          }
          fetchData();
        }, []);

        return (
          <div>
            <h2>CSV Data</h2>
            {loading ? (
              <p>Loading and downloading PDF...</p>
            ) : csvData ? (
              <table>
                <thead>
                  <tr>
                    {csvData.headers.map((header, idx) => (
                      <th key={idx}>{header}</th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {csvData.rows.map((row, idx) => (
                    <tr key={idx}>
                      {row.map((cell, cidx) => (
                        <td key={cidx}>{cell}</td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            ) : (
              <p>No CSV data found.</p>
            )}
          </div>
        );
      }

      const root = ReactDOM.createRoot(document.getElementById('root'));
      root.render(<CsvAndPdfViewer />);
    </script>
  </body>
</html>
