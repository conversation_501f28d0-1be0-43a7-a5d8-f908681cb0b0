const mqtt = require('mqtt');
const client = mqtt.connect('ws://192.168.0.144:9001', {
  username: 'resonate',
  password: 'Resonate@123',
});

function sleep(ms) {
  return new Promise(resolve => {
    setTimeout(resolve, ms);
  });
}
// Connection and client monitoring
client.on('connect', function () {
  console.log('Connected to MQTT broker');

  // Subscribe to system topics for monitoring clients and detailed information
  client.subscribe('$SYS/broker/clients/+'); // All client-related topics
  client.subscribe('$SYS/broker/connection/#'); // Connection events
  client.subscribe('$SYS/broker/clients/connected'); // Connected clients count
  client.subscribe('$SYS/broker/clients/disconnected'); // Disconnected clients
  client.subscribe('$SYS/broker/subscriptions/#'); // Topic subscriptions
  client.subscribe('$SYS/broker/clients/active'); // Currently active clients
  client.subscribe('$SYS/broker/client/+/connected'); // Individual client connections
  client.subscribe('$SYS/broker/load/#'); // Broker load information
  client.subscribe('$SYS/broker/messages/#'); // Message statistics
  client.subscribe('$SYS/broker/retained messages/count'); // Retained message count
  client.subscribe('$SYS/broker/uptime'); // Broker uptime

  // Subscribe to application topic
  client.subscribe('pico/testjig');

  // Log client details and system information
  client.on('message', function (topic, message) {
    if (topic.startsWith('$SYS/broker/')) {
      const messageStr = message.toString();
      console.log(`System Info - ${topic}: ${messageStr}`);

      // Parse and display detailed information
      if (topic === '$SYS/broker/clients/connected') {
        console.log(`Total connected clients: ${messageStr}`);
      } else if (topic.includes('disconnected')) {
        console.log(`Client disconnected: ${messageStr}`);
      } else if (topic === '$SYS/broker/clients/active') {
        console.log(`Active clients: ${messageStr}`);
      } else if (topic.match(/\$SYS\/broker\/client\/.*\/connected/)) {
        const clientId = topic.split('/')[3];
        console.log(`Client connected - ID: ${clientId}, Status: ${messageStr}`);
      } else if (topic.startsWith('$SYS/broker/load/')) {
        console.log(`Broker load - ${topic.split('/').pop()}: ${messageStr}`);
      } else if (topic.startsWith('$SYS/broker/messages/')) {
        console.log(`Message stats - ${topic.split('/').pop()}: ${messageStr}`);
      } else if (topic === '$SYS/broker/uptime') {
        console.log(`Broker uptime: ${messageStr}`);
      } else if (topic === '$SYS/broker/retained messages/count') {
        console.log(`Retained messages count: ${messageStr}`);
      }
    }

    // Log raw topic and message for debugging
    console.log(`Raw message - Topic: ${topic}, Message: ${message.toString()}`);
  });
});

// Log connection status
client.on('reconnect', function () {
  console.log('Reconnecting to MQTT broker...');
});

client.on('close', function () {
  console.log('MQTT connection closed');
});

client.on('offline', function () {
  console.log('MQTT client is offline');
});

client.on('error', function (err) {
  console.error('MQTT error:', err);
});
