import json
import re

import fitz  # PyMuPDF


def extract_invoice_data(pdf_path):
    doc = fitz.open(pdf_path)
    text = " ".join(page.get_text() for page in doc)
    text = re.sub(r'\s+', ' ', text)

    def extract(pattern, default=""):
        match = re.search(pattern, text)
        return match.group(1).strip() if match else default

    data = {
        "DeliveryChallan": extract(r"Delivery Note No\.?\s*([A-Z0-9\-]+)"),
        "Company": {
            "Name": "Resonate Systems Private Limited",
            "Address": "R2, First Floor, 31/6, Thayappa Garden, Bilekahalli,Bangalore,KA,560076",
            "GSTIN": extract(r"GSTIN/UIN:\s*(29[A-Z0-9]{13})"),
            "State": "Karnataka",
            "StateCode": "29",
            "Email": extract(r"E-Mail\s*:\s*([\w\.-]+@[\w\.-]+)"),
            "PAN": extract(r"Company's PAN\s*:\s*([A-Z]{5}\d{4}[A-Z])")
        },
        "Consignee": {
            "Name": "INGRAM MICRO INDIA PRIVATE LIMITED - 32",
            "Address": "INGRAM MICRO INDIA PRIVATE LIMITED - 32 PLOT# 35, WAREHOUSING CENTRE, BUILDING# 38/748, GANDHI NAGAR, KADAVANTHRA, COCHIN 682020",
            "GSTIN": extract(r"Consignee.*?GSTIN/UIN\s*:\s*(32[A-Z0-9]{13})"),
            "PAN": extract(r"Consignee.*?PAN/IT No\s*:\s*([A-Z]{5}\d{4}[A-Z])")
        },
        "Buyer": {
            "Name": "INGRAM MICRO INDIA PRIVATE LIMITED - 32",
            "Address": "INGRAM MICRO INDIA PRIVATE LIMITED - 32 PLOT# 35, WAREHOUSING CENTRE, BUILDING# 38/748, GANDHI NAGAR, KADAVANTHRA, COCHIN 682020",
            "GSTIN": extract(r"Buyer.*?GSTIN/UIN\s*:\s*(32[A-Z0-9]{13})"),
            "PAN": extract(r"Buyer.*?PAN/IT No\s*:\s*([A-Z]{5}\d{4}[A-Z])")
        },
        "DeliveryDetails": {
            "DeliveryNoteNo": extract(r"Delivery Note No\.?\s*([A-Z0-9\-]+)"),
            "ReferenceNoAndDate": extract(r"Reference No\. & Date\.?\s*([^\n]+)"),
            "BuyersOrderNo": extract(r"Buyer's Order No\.?\s*([^\n]+)"),
            "DispatchDocNo": extract(r"Dispatch Doc No\.?\s*([A-Z0-9\-]+)"),
            "DispatchedThrough": extract(r"Dispatched through\s*([^\n]+)"),
            "DispatchDate": extract(r"Dated\s*([0-9]{1,2}-[A-Za-z]{3}-[0-9]{2})"),
            "PaymentTerms": extract(r"Mode/Terms of Payment\s*([^\n]+)"),
            "OtherReferencesDate": extract(r"Other References Dated\s*([0-9]{1,2}-[A-Za-z]{3}-[0-9]{2})"),
            "Destination": extract(r"Destination\s*([^\n]+)"),
            "TermsOfDelivery": ""
        },
        "Goods": [],
        "TotalQuantity": extract(r"Total\s*([\d\.]+\s*NOS)"),
        "Jurisdiction": "Bangalore",
        "DocumentNote": "This is a Computer Generated Document",
        "Signature": "Authorised Signatory",
        "Condition": "Recd. in Good Condition",
        "E_O_E": True
    }

    goods_match = re.search(
        r"(\d+)\s+(RSNT-[A-Z0-9\-]+)\s+([\d\.]+)\s+(NOS|PCS)\s+(\d{8})\s+(RESONATE RouterUPS.*?)IGST @ 18%",
        text
    )
    if goods_match:
        data["Goods"].append({
            "Description": goods_match.group(2),
            "Quantity": float(goods_match.group(3)),
            "Unit": goods_match.group(4),
            "HSN_SAC": goods_match.group(5),
            "Details": goods_match.group(6).strip(),
            "Tax": "IGST @ 18%"
        })

    return data


# Example usage
invoice_data = extract_invoice_data("./invoices/RSNT26D0127 - Ingram 32.pdf")
print(json.dumps(invoice_data, indent=2))