const mqtt = require('mqtt');
const client = mqtt.connect('ws://192.168.0.144:9001', {
  username: 'resonate',
  password: 'Resonate@123',
});

let currentState = 0; // Track sequence state (0-4)
let isTestStarted = false;
let isStateJustChanged = false;
let enableState3Testing = true; // Changed to true to enable state 3 testing
let serialNumber = true; //serial number present or not
let remainingStates = enableState3Testing ? [1, 2, 3] : [1, 2]; // Initialize based on enableState3Testing
const response = {
  device_id: '7825a63a6f612d4f',
  topic: 'testjig/sample',
  timestamp: Math.floor(Date.now() / 1000),
};

function sleep(ms) {
  return new Promise(resolve => {
    setTimeout(resolve, ms);
  });
}

// Add these variables at the top with other declarations
client.on('connect', async function () {
  console.log('Connected to MQTT broker');

  // Subscribe to the topic pico/testing
  client.subscribe('testjig/clients', function (err) {
    if (err) {
      console.error('Failed to subscribe to pico/testing:', err);
    } else {
      console.log('Subscribed to testjig/clients');
    }
  });

  client.publish('testjig/clients', JSON.stringify(response));

  client.subscribe(response.topic, function (err) {
    if (err) {
      console.error('Failed to subscribe to', response.topic, ':', err);
    } else {
      console.log('Subscribed to', response.topic);
    }
  });

  // Monitor BP values and send state 7 actions
  let state7Payload = {
    state: 7,
    //action: 'charge',
    action: 'discharge',
  };
  client.publish(response.topic, JSON.stringify(state7Payload));

  await sleep(10000);

  state7Payload = { state: 4 };

  client.publish(response.topic, JSON.stringify(state7Payload));
  // Sleep for 10 second
  await sleep(10000);

  // Send initial test data with state 0
  const initialPayload = {
    state: currentState,
    pcb_status: Array.from({ length: 9 }, () => Math.random() > 0.5),
    bp: Array.from({ length: 9 }, () => Math.floor(Math.random() * 101)),
    ...(serialNumber && {
      sn: `SN-${Math.floor(Math.random() * 100000).toString()}`,
    }),
  };
  client.publish(response.topic, JSON.stringify(initialPayload));

  isTestStarted = true;

  // Start test sequence after initial setup (state 0)
  setTimeout(() => {
    currentState = getNextRandomState();
    const stateChangePayload = { state: currentState };
    client.publish(response.topic, JSON.stringify(stateChangePayload));
    isStateJustChanged = true;
  }, 2000);

  // Interval for sequence data
  setInterval(() => {
    if (currentState === 4) {
      clearInterval(this);
      return;
    }

    // Send sequence data directly as array
    const sequenceData = generateRandomMessage();
    client.publish(response.topic, JSON.stringify(sequenceData));

    // Modified state transition logic
    if (elapsedTime >= 15) {
      currentState = getNextRandomState();
      elapsedTime = 0;
      const stateChangePayload = { state: currentState };
      client.publish(response.topic, JSON.stringify(stateChangePayload));
      isStateJustChanged = true;
    }
  }, 2000);
});

// Log connection status
client.on('reconnect', function () {
  console.log('Reconnecting to MQTT broker...');
});

client.on('close', function () {
  console.log('MQTT connection closed');
});

client.on('offline', function () {
  console.log('MQTT client is offline');
});

client.on('error', function (err) {
  console.error('MQTT error:', err);
});

// Handle incoming messages from the topic dynamic topics
client.on('message', function (topic, message) {
  if (topic === response.topic) {
    console.log('Received message from', response.topic, ':', message.toString());
  }
});

let elapsedTime = 0; // Initialize elapsed time
let isChargingSequence = true; // Toggle between charging and discharging

function generateRandomMessage() {
  const totalPCs = 9; // Total pc values
  const maxChannels = 3; // Total channels
  const pcsPerChannel = Math.floor(totalPCs / maxChannels); // Calculate how many pcs each channel can accommodate
  const channels = Array.from({ length: maxChannels }, (_, i) => i + 1);
  const randomValues = () =>
    Array.from({ length: pcsPerChannel }, () => parseFloat((Math.random() * 10).toFixed(2)));
  const calculateProgress = (start, end, elapsed) =>
    Array.from({ length: pcsPerChannel }, () =>
      parseFloat((start + ((end - start) * elapsed) / 30).toFixed(2))
    );

  // Increment elapsed time by 1 minute
  elapsedTime += 1;

  // Generate data for each channel
  const data = channels
    .map(channel => {
      const charging = {
        C: randomValues(),
        V: randomValues(),
      };

      const discharging = {
        C: randomValues(),
        V: randomValues(),
      };

      let progress, status;

      // Randomly generate status with 80% chance of being true
      const randomStatus = () =>
        Array(pcsPerChannel)
          .fill()
          .map(() => Math.random() < 0.8);

      switch (currentState) {
        case 1: // Charging only (0-100)
          progress = calculateProgress(0, 100, elapsedTime);
          status = randomStatus();
          return {
            channel,
            progress,
            elapsed_time: Array(pcsPerChannel).fill(elapsedTime),
            charging,
            status,
          };

        case 2: // Discharging only (100-0)
          progress = calculateProgress(100, 0, elapsedTime);
          status = randomStatus();
          return {
            channel,
            progress,
            elapsed_time: Array(pcsPerChannel).fill(elapsedTime),
            discharging,
            status,
          };

        case 3: // Both charging and discharging on each channel
          progress = calculateProgress(0, 100, elapsedTime);
          status = randomStatus();
          return {
            channel,
            progress,
            elapsed_time: Array(pcsPerChannel).fill(elapsedTime),
            charging,
            discharging,
            status,
          };

        default:
          return null;
      }
    })
    .filter(item => item !== null);

  return data;
}

// Add this function before client.on('connect')
function getNextRandomState() {
  if (remainingStates.length === 0) {
    // Reset states if we've used all states except state 4
    remainingStates = enableState3Testing ? [1, 2, 3] : [1, 2];
    return 4; // Return completion state
  }
  const randomIndex = Math.floor(Math.random() * remainingStates.length);
  return remainingStates.splice(randomIndex, 1)[0];
}
