const fs = require("fs");
const path = require("path");
const pdf = require("pdf-parse");
const { extractFieldsFromText } = require("./utils/enhancedExtractText");
const { extractTextWithFallback, enhancedPreprocessing, validateTextQuality } = require("./utils/multiPdfParser");

// Directory containing the PDF files
const invoicesDir = path.resolve(__dirname, "invoices");

// Get all PDF files in the directory
const pdfFiles = fs.readdirSync(invoicesDir).filter(file => file.toLowerCase().endsWith('.pdf'));

// Process each PDF file
async function processAllPdfs() {
    const results = [];
    let successCount = 0;
    let errorCount = 0;

    console.log(`\n🚀 Starting to process ${pdfFiles.length} PDF files...\n`);

    for (const pdfFile of pdfFiles) {
        const filePath = path.join(invoicesDir, pdfFile);
        console.log(`📄 Processing: ${pdfFile}`);

        try {
            // Extract text from PDF using improved multi-library approach
            let text = await extractTextWithFallback(filePath);

            // Validate text quality
            const quality = validateTextQuality(text);
            console.log(`   📊 Text quality: ${quality.quality}% (${quality.lineCount} lines)`);

            // Extract fields and items using enhanced extraction
            const result = extractFieldsFromText(text);

            // Determine document type from the result structure
            let documentType = "UNKNOWN";
            let fieldsCount = 0;
            let itemsCount = 0;
            let hasCompanyInfo = false;
            let hasConsigneeInfo = false;
            let hasBuyerInfo = false;

            if (result.DeliveryChallan) {
                documentType = "DELIVERY_CHALLAN";
                fieldsCount = Object.keys(result).length;
                itemsCount = result.Goods ? result.Goods.length : 0;
                hasCompanyInfo = !!(result.Company && result.Company.Name);
                hasConsigneeInfo = !!(result.Consignee && result.Consignee.Name);
                hasBuyerInfo = !!(result.Buyer && result.Buyer.Name);
            } else if (result.IRN || result.AckNo) {
                documentType = "TAX_INVOICE";
                fieldsCount = Object.keys(result).length;
                itemsCount = result.Goods ? result.Goods.length : 0;
                hasCompanyInfo = !!(result.Company && result.Company.Name);
                hasConsigneeInfo = !!(result.Consignee && result.Consignee.Name);
                hasBuyerInfo = !!(result.Buyer && result.Buyer.Name);
            } else if (result.JobOrder) {
                documentType = "JOB_ORDER";
                fieldsCount = Object.keys(result).length;
                itemsCount = result.Goods ? result.Goods.length : 0;
                hasCompanyInfo = !!(result.JobOrder && result.JobOrder.Company);
                hasConsigneeInfo = !!(result.Consignee && result.Consignee.Company);
                hasBuyerInfo = !!(result.Buyer && result.Buyer.Company);
            } else if (result.PurchaseOrder) {
                documentType = "PURCHASE_ORDER";
                fieldsCount = Object.keys(result).length;
                itemsCount = result.Items ? result.Items.length : 0;
                hasCompanyInfo = !!(result.Buyer && result.Buyer.Company);
                hasConsigneeInfo = false;
                hasBuyerInfo = !!(result.Vendor && result.Vendor.Company);
            } else if (result.fields) {
                // Fallback for generic structure
                documentType = result.fields.documentType || "UNKNOWN";
                fieldsCount = Object.keys(result.fields).length;
                itemsCount = result.items ? result.items.length : 0;
                hasCompanyInfo = !!(result.fields.companyName && result.fields.companyAddress);
                hasConsigneeInfo = !!(result.fields.consigneeName && result.fields.consigneeAddress);
                hasBuyerInfo = !!(result.fields.buyerName && result.fields.buyerAddress);
            }

            // Add filename and processing metadata
            result.filename = pdfFile;
            result.processedAt = new Date().toISOString();
            result.extractionSummary = {
                documentType: documentType,
                fieldsExtracted: fieldsCount,
                itemsExtracted: itemsCount,
                hasCompanyInfo: hasCompanyInfo,
                hasConsigneeInfo: hasConsigneeInfo,
                hasBuyerInfo: hasBuyerInfo
            };

            // Add to results array
            results.push(result);
            successCount++;

            // Save individual result for debugging
            const outputPath = path.join(__dirname, `output_${pdfFile.replace('.pdf', '.json')}`);
            fs.writeFileSync(outputPath, JSON.stringify(result, null, 2));

            console.log(`   ✅ Success: ${result.extractionSummary.fieldsExtracted} fields, ${result.extractionSummary.itemsExtracted} items`);
            console.log(`   📁 Individual output: output_${pdfFile.replace('.pdf', '.json')}`);

        } catch (error) {
            console.error(`   ❌ Error processing ${pdfFile}:`, error.message);
            errorCount++;
            results.push({
                filename: pdfFile,
                error: error.message,
                processedAt: new Date().toISOString(),
                extractionSummary: {
                    documentType: "ERROR",
                    fieldsExtracted: 0,
                    itemsExtracted: 0,
                    hasCompanyInfo: false,
                    hasConsigneeInfo: false,
                    hasBuyerInfo: false
                }
            });
        }
    }

    // Write combined results to output.json
    const finalOutput = {
        processingMetadata: {
            totalFiles: pdfFiles.length,
            successfullyProcessed: successCount,
            errors: errorCount,
            processedAt: new Date().toISOString()
        },
        results: results
    };

    fs.writeFileSync("output.json", JSON.stringify(finalOutput, null, 2));

    console.log(`\n📊 Processing Summary:`);
    console.log(`   Total files: ${pdfFiles.length}`);
    console.log(`   Successfully processed: ${successCount}`);
    console.log(`   Errors: ${errorCount}`);
    console.log(`\n✅ Combined output written to output.json`);
}

// Run the process
processAllPdfs().catch(error => {
    console.error("Error:", error);
});