const fs = require("fs");
const path = require("path");
const pdf = require("pdf-parse");

// Directory containing the PDF files
const invoicesDir = path.resolve(__dirname, "invoices");

// Get all PDF files in the directory
const pdfFiles = fs.readdirSync(invoicesDir).filter(file => file.toLowerCase().endsWith('.pdf'));

// Function to extract text from a PDF file
async function extractTextFromPdf(filePath) {
    const dataBuffer = fs.readFileSync(filePath);
    const data = await pdf(dataBuffer);
    return data.text;
}

// Process each PDF file
async function processAllPdfs() {
    for (const pdfFile of pdfFiles) {
        const filePath = path.join(invoicesDir, pdfFile);
        console.log(`\n\n========== Processing: ${pdfFile} ==========`);
        
        try {
            const text = await extractTextFromPdf(filePath);
            
            // Write the raw text to a file for analysis
            const outputPath = path.join(__dirname, `raw_text_${pdfFile.replace('.pdf', '.txt')}`);
            fs.writeFileSync(outputPath, text);
            
            // Display first 500 characters of the text for quick preview
            console.log("Preview of extracted text:");
            console.log(text.substring(0, 500) + "...");
            console.log(`\nFull text saved to: ${outputPath}`);
        } catch (error) {
            console.error(`Error processing ${pdfFile}:`, error);
        }
    }
}

// Run the process
processAllPdfs().then(() => {
    console.log("\nAll PDF files processed.");
}).catch(error => {
    console.error("Error:", error);
});