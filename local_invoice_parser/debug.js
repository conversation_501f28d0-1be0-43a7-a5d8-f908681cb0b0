const fs = require("fs");
const path = require("path");
const { extractTextWithFallback, enhancedPreprocessing, validateTextQuality } = require("./utils/multiPdfParser");
const { extractFieldsFromText } = require("./utils/enhancedExtractText");
const { extractDeliveryChallanSimple } = require("./utils/simpleExtractor");

async function debugSingleFile() {
    const filePath = path.join(__dirname, "invoices", "RSNT26D0127 - Ingram 32.pdf");
    
    console.log("=== DEBUGGING SINGLE FILE ===");
    console.log("File:", filePath);
    
    try {
        // Extract text
        let text = await extractTextWithFallback(filePath);
        console.log("\n=== RAW TEXT (first 500 chars) ===");
        console.log(text.substring(0, 500));
        
        // Skip preprocessing for now
        console.log("\n=== SKIPPING PREPROCESSING ===");
        
        // Check quality
        const quality = validateTextQuality(text);
        console.log("\n=== QUALITY METRICS ===");
        console.log(quality);
        
        // Split into lines for analysis
        const lines = text.split(/\n/).map(l => l.trim()).filter(Boolean);
        console.log("\n=== FIRST 20 LINES ===");
        lines.slice(0, 20).forEach((line, i) => {
            console.log(`${i + 1}: "${line}"`);
        });
        
        // Test document type detection
        const firstTenLines = lines.slice(0, 15).join(' ').toLowerCase();
        console.log("\n=== DOCUMENT TYPE DETECTION ===");
        console.log("First 15 lines joined:", firstTenLines);
        console.log("Contains 'delivery challan':", firstTenLines.includes('delivery challan'));
        console.log("Contains 'delivery note no':", lines.some(line => line.toLowerCase().includes('delivery note no')));
        
        // Try extraction with debugging
        console.log("\n=== EXTRACTION RESULT ===");

        // Test document type detection directly
        const debugLines = text.split(/\n/).map(l => l.trim()).filter(Boolean);
        const debugFirstTenLines = debugLines.slice(0, 15).join(' ').toLowerCase();

        let docType = 'UNKNOWN';
        if (debugFirstTenLines.includes('purchase order') || debugFirstTenLines.includes('po no')) {
            docType = 'PURCHASE_ORDER';
        } else if (debugFirstTenLines.includes('delivery challan')) {
            docType = 'DELIVERY_CHALLAN';
        } else if (debugFirstTenLines.includes('tax invoice') || debugFirstTenLines.includes('e-invoice')) {
            docType = 'TAX_INVOICE';
        } else if (debugFirstTenLines.includes('b2c job order') || debugFirstTenLines.includes('job order')) {
            docType = 'JOB_ORDER';
        }

        console.log("Document type should be:", docType);

        // Test simple extractor
        console.log("\n=== TESTING SIMPLE EXTRACTOR ===");
        const simpleResult = extractDeliveryChallanSimple(text);
        console.log("Simple extractor - Company name:", simpleResult.Company?.Name || "Not found");
        console.log("Simple extractor - Delivery challan:", simpleResult.DeliveryChallan || "Not found");
        console.log("Simple extractor - GSTIN:", simpleResult.Company?.GSTIN || "Not found");
        console.log("Simple extractor - Goods count:", simpleResult.Goods?.length || 0);

        if (simpleResult.Goods && simpleResult.Goods.length > 0) {
            console.log("First item:", simpleResult.Goods[0]);
        }

        const result = extractFieldsFromText(text);
        console.log("\n=== COMPLEX EXTRACTOR ===");
        console.log("Actual result keys:", Object.keys(result));
        console.log("Document type in result:", result.extractionSummary?.documentType || "Not found");
        console.log("Fields extracted:", Object.keys(result).length);
        console.log("Company name:", result.Company?.Name || "Not found");
        console.log("Delivery challan:", result.DeliveryChallan || "Not found");
        
    } catch (error) {
        console.error("Error:", error);
    }
}

debugSingleFile();