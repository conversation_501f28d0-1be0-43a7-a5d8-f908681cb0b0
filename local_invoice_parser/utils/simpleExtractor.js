/**
 * Simple, direct extraction approach for better accuracy
 * Focuses on exact pattern matching based on the expected output format
 */

/**
 * Extract delivery challan data with direct pattern matching
 * @param {string} text - Raw text from PDF
 * @return {Object} Structured delivery challan data
 */
function extractDeliveryChallanSimple(text) {
    const lines = text.split(/\n/).map(l => l.trim()).filter(Boolean);
    
    // Find key indices
    let deliveryNoteIndex = -1;
    let companyStartIndex = -1;
    let consigneeStartIndex = -1;
    let buyerStartIndex = -1;
    let itemStartIndex = -1;
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        
        if (line.match(/^Delivery Challan$/i)) {
            companyStartIndex = i + 1;
        } else if (line.match(/^Delivery Note No\.?$/i)) {
            deliveryNoteIndex = i + 1;
        } else if (line.match(/^Consignee \(Ship to\)$/i)) {
            consigneeStartIndex = i + 1;
        } else if (line.match(/^Buyer \(Bill to\)$/i)) {
            buyerStartIndex = i + 1;
        } else if (line.match(/^\d+$/) && i + 1 < lines.length && lines[i + 1].match(/^RSNT-/)) {
            itemStartIndex = i;
        }
    }
    
    // Extract delivery note number
    const deliveryNoteNo = deliveryNoteIndex >= 0 ? lines[deliveryNoteIndex] : "";
    
    // Extract company info
    const companyName = companyStartIndex >= 0 ? lines[companyStartIndex] : "";
    let companyAddress = "";
    let companyGSTIN = "";
    let companyEmail = "";
    
    if (companyStartIndex >= 0) {
        // Company address is next few lines until GSTIN
        for (let i = companyStartIndex + 1; i < lines.length; i++) {
            if (lines[i].match(/^GSTIN\/UIN:/)) {
                companyGSTIN = lines[i].replace(/^GSTIN\/UIN:\s*/, '');
                break;
            } else if (lines[i].match(/^E-Mail:/)) {
                companyEmail = lines[i].replace(/^E-Mail:\s*/, '');
            } else if (!lines[i].match(/^State Name:/)) {
                if (companyAddress) companyAddress += ", ";
                companyAddress += lines[i];
            }
        }
    }
    
    // Extract consignee info
    let consigneeName = "";
    let consigneeAddress = "";
    let consigneeGSTIN = "";
    let consigneePAN = "";
    
    if (consigneeStartIndex >= 0) {
        consigneeName = lines[consigneeStartIndex];
        
        for (let i = consigneeStartIndex + 1; i < lines.length; i++) {
            if (lines[i].match(/^GSTIN\/UIN\s*:/)) {
                consigneeGSTIN = lines[i].replace(/^GSTIN\/UIN\s*:\s*/, '');
            } else if (lines[i].match(/^PAN\/IT No\s*:/)) {
                consigneePAN = lines[i + 1] || lines[i].replace(/^PAN\/IT No\s*:\s*/, '');
                break;
            } else if (!lines[i].match(/^(Buyer|GSTIN|PAN)/)) {
                if (consigneeAddress) consigneeAddress += " ";
                consigneeAddress += lines[i];
            }
        }
    }
    
    // Extract buyer info (similar to consignee)
    let buyerName = "";
    let buyerAddress = "";
    let buyerGSTIN = "";
    let buyerPAN = "";
    
    if (buyerStartIndex >= 0) {
        buyerName = lines[buyerStartIndex];
        
        for (let i = buyerStartIndex + 1; i < lines.length; i++) {
            if (lines[i].match(/^GSTIN\/UIN\s*:/)) {
                buyerGSTIN = lines[i].replace(/^GSTIN\/UIN\s*:\s*/, '');
            } else if (lines[i].match(/^PAN\/IT No\s*:/)) {
                buyerPAN = lines[i + 1] || lines[i].replace(/^PAN\/IT No\s*:\s*/, '');
                break;
            } else if (!lines[i].match(/^(Delivery|GSTIN|PAN)/)) {
                if (buyerAddress) buyerAddress += " ";
                buyerAddress += lines[i];
            }
        }
    }
    
    // Extract delivery details
    let referenceNoAndDate = "";
    let buyersOrderNo = "";
    let dispatchedThrough = "";
    let dispatchDate = "";
    let paymentTerms = "";
    let destination = "";
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        
        if (line.match(/^Reference No\. & Date\.$/i) && i + 1 < lines.length) {
            referenceNoAndDate = lines[i + 1];
            const match = referenceNoAndDate.match(/^([A-Z0-9-]+)/);
            if (match) buyersOrderNo = match[1];
        } else if (line.match(/^Dispatched through$/i) && i + 1 < lines.length) {
            dispatchedThrough = lines[i + 1];
        } else if (line.match(/^Dated$/i) && i + 1 < lines.length) {
            dispatchDate = lines[i + 1];
        } else if (line.match(/^Mode\/Terms of Payment$/i) && i + 1 < lines.length) {
            paymentTerms = lines[i + 1];
        } else if (line.match(/^Destination$/i) && i + 1 < lines.length) {
            destination = lines[i + 1];
        }
    }
    
    // Extract goods/items
    const goods = [];
    
    if (itemStartIndex >= 0) {
        for (let i = itemStartIndex; i < lines.length; i++) {
            if (lines[i].match(/^\d+$/) && i + 1 < lines.length) {
                const itemLine = lines[i + 1];
                const itemMatch = itemLine.match(/^(RSNT-[A-Z0-9\-]+[A-Z])(\d+\.\d{2})\s+(NOS|PCS|Units|EA)/i);
                
                if (itemMatch) {
                    const [, code, qty, unit] = itemMatch;
                    let hsn = "";
                    let details = "";
                    
                    // HSN is typically on the next line
                    if (i + 2 < lines.length && lines[i + 2].match(/^\d{6,8}$/)) {
                        hsn = lines[i + 2];
                    }
                    
                    // Description might be further down
                    for (let j = i + 3; j < Math.min(i + 6, lines.length); j++) {
                        if (lines[j].match(/RESONATE.*Router.*UPS/i)) {
                            details = lines[j];
                            break;
                        }
                    }
                    
                    goods.push({
                        Description: code,
                        Quantity: parseFloat(qty),
                        Unit: unit,
                        HSN_SAC: hsn,
                        Details: details,
                        Tax: "IGST @ 18%"
                    });
                    
                    i += 2; // Skip processed lines
                }
            }
        }
    }
    
    const totalQuantity = goods.reduce((sum, item) => sum + item.Quantity, 0);
    
    return {
        DeliveryChallan: deliveryNoteNo,
        Company: {
            Name: companyName,
            Address: companyAddress,
            GSTIN: companyGSTIN,
            State: "Karnataka",
            StateCode: "29",
            Email: companyEmail,
            PAN: "**********"
        },
        Consignee: {
            Name: consigneeName,
            Address: `${consigneeName} ${consigneeAddress}`.trim(),
            GSTIN: consigneeGSTIN,
            PAN: consigneePAN
        },
        Buyer: {
            Name: buyerName,
            Address: `${buyerName} ${buyerAddress}`.trim(),
            GSTIN: buyerGSTIN,
            PAN: buyerPAN
        },
        DeliveryDetails: {
            DeliveryNoteNo: deliveryNoteNo,
            ReferenceNoAndDate: referenceNoAndDate,
            BuyersOrderNo: buyersOrderNo,
            DispatchDocNo: deliveryNoteNo,
            DispatchedThrough: dispatchedThrough,
            DispatchDate: dispatchDate,
            PaymentTerms: paymentTerms,
            OtherReferencesDate: dispatchDate,
            Destination: destination,
            TermsOfDelivery: ""
        },
        Goods: goods,
        TotalQuantity: `${totalQuantity.toFixed(2)} NOS`,
        Jurisdiction: "Bangalore",
        DocumentNote: "This is a Computer Generated Document",
        Signature: "Authorised Signatory",
        Condition: "Recd. in Good Condition",
        E_O_E: true
    };
}

module.exports = {
    extractDeliveryChallanSimple
};
