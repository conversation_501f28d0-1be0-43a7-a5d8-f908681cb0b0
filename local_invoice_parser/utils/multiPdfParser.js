/**
 * Multi-library PDF parser for better text extraction accuracy
 * Uses multiple PDF parsing libraries as fallbacks
 */

const fs = require('fs');
const pdf = require('pdf-parse');

/**
 * Extract text using multiple PDF parsing approaches
 * @param {string} filePath - Path to PDF file
 * @return {Promise<string>} Extracted text
 */
async function extractTextWithFallback(filePath) {
    const dataBuffer = fs.readFileSync(filePath);

    try {
        // Primary method: pdf-parse with default settings
        const data = await pdf(dataBuffer);
        let text = data.text;

        // Always return the text from pdf-parse as it's most reliable
        // The quality check will be done later
        return text;

    } catch (error) {
        console.error('Primary PDF extraction failed:', error.message);

        // Fallback to basic text extraction
        try {
            const data = await pdf(dataBuffer, {
                normalizeWhitespace: false,
                disableCombineTextItems: true
            });
            return data.text;
        } catch (fallbackError) {
            console.error('Fallback extraction also failed:', fallbackError.message);
            return '';
        }
    }
}

/**
 * Try alternative PDF extraction methods
 * @param {<PERSON>uffer} dataBuffer - PDF file buffer
 * @return {Promise<string>} Extracted text
 */
async function tryAlternativeExtraction(dataBuffer) {
    try {
        // Try pdf2json if available
        const pdf2json = require('pdf2json');
        return await extractWithPdf2json(dataBuffer);
    } catch (error) {
        console.log('pdf2json not available or failed, using basic extraction');
        
        // Fallback to basic pdf-parse with different options
        try {
            const data = await pdf(dataBuffer, {
                // Try with different options
                normalizeWhitespace: false,
                disableCombineTextItems: false
            });
            return data.text;
        } catch (finalError) {
            console.error('All PDF extraction methods failed:', finalError.message);
            return '';
        }
    }
}

/**
 * Extract text using pdf2json
 * @param {Buffer} dataBuffer - PDF file buffer
 * @return {Promise<string>} Extracted text
 */
function extractWithPdf2json(dataBuffer) {
    return new Promise((resolve, reject) => {
        const PDFParser = require('pdf2json');
        const pdfParser = new PDFParser();
        
        pdfParser.on('pdfParser_dataError', errData => {
            reject(new Error(errData.parserError));
        });
        
        pdfParser.on('pdfParser_dataReady', pdfData => {
            try {
                let text = '';
                
                // Extract text from each page
                pdfData.formImage.Pages.forEach(page => {
                    page.Texts.forEach(textItem => {
                        textItem.R.forEach(run => {
                            text += decodeURIComponent(run.T) + ' ';
                        });
                        text += '\n';
                    });
                });
                
                resolve(text);
            } catch (error) {
                reject(error);
            }
        });
        
        pdfParser.parseBuffer(dataBuffer);
    });
}

/**
 * Enhanced text preprocessing for better parsing
 * @param {string} text - Raw extracted text
 * @return {string} Preprocessed text
 */
function enhancedPreprocessing(text) {
    // Minimal preprocessing to avoid breaking the structure
    // Just normalize line endings
    let processed = text.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

    // Remove excessive spaces within lines but preserve line breaks
    processed = processed.replace(/[ \t]+/g, ' ');

    // Remove leading/trailing spaces from each line
    processed = processed.split('\n').map(line => line.trim()).join('\n');

    return processed;
}

/**
 * Validate extracted text quality
 * @param {string} text - Extracted text
 * @return {Object} Quality metrics
 */
function validateTextQuality(text) {
    const lines = text.split('\n').filter(line => line.trim().length > 0);
    const hasCompanyName = /Resonate Systems Private Limited/i.test(text);
    const hasGSTIN = /GSTIN\/UIN\s*:\s*[0-9A-Z]{15}/i.test(text);
    const hasItemCodes = /RSNT-[A-Z0-9\-]+/i.test(text);
    
    return {
        lineCount: lines.length,
        hasCompanyName,
        hasGSTIN,
        hasItemCodes,
        quality: (hasCompanyName ? 25 : 0) + (hasGSTIN ? 25 : 0) + (hasItemCodes ? 25 : 0) + (lines.length > 20 ? 25 : 0)
    };
}

module.exports = {
    extractTextWithFallback,
    enhancedPreprocessing,
    validateTextQuality
};