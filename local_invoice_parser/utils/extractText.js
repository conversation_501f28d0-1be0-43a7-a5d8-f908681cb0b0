function extractFieldsFromText (text) {
    const lines = text.split (/\n/).map (l => l.trim ()).filter (Boolean);
    const fields = {};
    const items = [];

    const fieldPatterns = [
        [/GSTIN\/?UIN\s*[:\-]?\s*([0-9A-Z]{15})/i, "GSTIN"],
        [/PAN\/?IT\s*No\s*[:\-]?\s*([A-Z0-9]{10})/i, "PAN"],
        [/Delivery\s*Note\s*No\.?\s*[:\-]?\s*(.*)/i, "Delivery Note No"],
        [/Reference\s*No.*[:\-]?\s*(.*)/i, "Reference No"],
        [/Dispatch(ed)?\s*Through\s*[:\-]?\s*(.*)/i, "Dispatched Through"],
        [/Destination\s*[:\-]?\s*(.*)/i, "Destination"],
        [/Date\s*[:\-]?\s*(\d{1,2}-[A-Za-z]{3}-\d{2,4})/, "Date"]
    ];

    for (let i = 0; i < lines.length; i++) {
        for (const [regex, key] of fieldPatterns) {
            const match = lines[i].match (regex);
            if (match && match[1] && !fields[key]) {
                let value = match[1].trim ();
                if (value === "." || value === ":" || value.toLowerCase () === "ed") {
                    const nextLine = lines[i + 1]?.trim ();
                    if (nextLine && !nextLine.match (/^[A-Z\s]{4,}$/)) {
                        value = nextLine;
                        i++;
                    }
                }
                fields[key] = value;
            }

        }
    }

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const itemMatch = line.match (/^(?:\d+\s+)?(RSNT-[A-Z0-9\-]+)\s+(\d{6,8})\s+(\d+\.\d{2})\s+(NOS|PCS|Units)/i);
        if (itemMatch) {
            const [, code, hsn, qty, unit] = itemMatch;

            // Grab 1–2 lines below for description
            const descLines = [];
            for (let j = 1; j <= 2 && i + j < lines.length; j++) {
                const desc = lines[i + j].trim ();
                if (/^(IGST|CGST|SGST|Total)/i.test (desc)) break;
                descLines.push (desc);
            }

            items.push ({
                code,
                hsn,
                qty,
                unit,
                description: descLines.join (" ")
            });

            i += descLines.length; // skip description lines
        }
    }


    return {fields, items};
}

module.exports = {extractFieldsFromText};